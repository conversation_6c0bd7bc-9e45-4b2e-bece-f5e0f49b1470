import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

/**
 * 搜索handover订单 - 根据pudo_internal_code查询可交接的订单
 * POST /api/operations/handover/search
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.pudo_store_id) {
      return NextResponse.json(
        { code: 401, message: 'Unauthorized or no store associated', data: null },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { pudoInternalCode } = body;

    if (!pudoInternalCode || !pudoInternalCode.trim()) {
      return NextResponse.json(
        { code: 400, message: 'PUDO internal code is required', data: null },
        { status: 400 }
      );
    }

    const searchCode = pudoInternalCode.trim();

    // 并行查询return_order和pickup_order
    const [returnOrder, pickupOrder] = await Promise.all([
      // 查询return_order: 状态为inbound_pudo且pudo_internal_code匹配
      prisma.returnOrder.findFirst({
        where: {
          pudo_store_id: BigInt(session.user.pudo_store_id),
          pudo_internal_code: searchCode,
          status: 'inbound_pudo'
        },
        include: {
          client: {
            select: {
              name: true,
              code: true
            }
          },
          return_box: {
            select: {
              pudo_internal_code: true,
              status: true
            }
          }
        }
      }),
      
      // 查询pickup_order: 状态为expired且pudo_internal_code匹配
      prisma.pickupOrder.findFirst({
        where: {
          pudo_store_id: BigInt(session.user.pudo_store_id),
          pudo_internal_code: searchCode,
          status: 'expired'
        },
        include: {
          client: {
            select: {
              name: true,
              code: true
            }
          }
        }
      })
    ]);

    // 如果找到return_order
    if (returnOrder) {
      const senderInfo = returnOrder.sender as any;
      const receiverInfo = returnOrder.receiver as any;
      
      return NextResponse.json({
        code: 200,
        message: 'Return order found',
        data: {
          orderNo: returnOrder.reference_number,
          orderType: 'return',
          status: 'ready',
          customer: returnOrder.client?.name || 'N/A',
          type: 'return',
          info: `Return order, ready for handover`,
          details: {
            id: returnOrder.id.toString(),
            referenceNumber: returnOrder.reference_number,
            returnTrackingNumber: returnOrder.return_tracking_number,
            weight: returnOrder.weight,
            needBox: returnOrder.need_box,
            createAt: returnOrder.create_at,
            inboundAt: returnOrder.inbound_at,
            senderInfo: {
              name: senderInfo?.name || '',
              email: senderInfo?.email || '',
              phone: senderInfo?.phone || '',
              address: senderInfo?.address || ''
            },
            receiverInfo: {
              name: receiverInfo?.name || '',
              email: receiverInfo?.email || '',
              phone: receiverInfo?.phone || '',
              address: receiverInfo?.address || ''
            },
            returnBox: returnOrder.return_box ? {
              pudoInternalCode: returnOrder.return_box.pudo_internal_code,
              status: returnOrder.return_box.status
            } : null
          }
        }
      });
    }

    // 如果找到pickup_order
    if (pickupOrder) {
      const senderInfo = pickupOrder.sender as any;
      const receiverInfo = pickupOrder.receiver as any;
      
      return NextResponse.json({
        code: 200,
        message: 'Pickup order found',
        data: {
          orderNo: pickupOrder.tracking_number,
          orderType: 'pickup',
          status: 'ready',
          customer: pickupOrder.client?.name || 'N/A',
          type: 'expired',
          info: `${pickupOrder.is_cod ? 'COD order' : 'Normal order'}, expired and ready for handover`,
          details: {
            id: pickupOrder.id.toString(),
            trackingNumber: pickupOrder.tracking_number,
            weight: pickupOrder.weight,
            isCod: pickupOrder.is_cod,
            codValue: pickupOrder.cod_value?.toString(),
            codCurrency: pickupOrder.cod_value_currency,
            isAuth: pickupOrder.is_auth,
            pickupCode: pickupOrder.pickup_code,
            createAt: pickupOrder.create_at,
            inboundAt: pickupOrder.inbound_at,
            expiredAt: pickupOrder.expired_at,
            senderInfo: {
              name: senderInfo?.name || '',
              email: senderInfo?.email || '',
              phone: senderInfo?.phone || '',
              address: senderInfo?.address || ''
            },
            receiverInfo: {
              name: receiverInfo?.name || '',
              email: receiverInfo?.email || '',
              phone: receiverInfo?.phone || '',
              address: receiverInfo?.address || '',
              dni: receiverInfo?.dni || ''
            }
          }
        }
      });
    }

    // 没有找到匹配的订单
    return NextResponse.json({
      code: 404,
      message: 'No handover order found with the provided code',
      data: null
    });

  } catch (error) {
    console.error('搜索handover订单失败:', error);
    return NextResponse.json(
      { code: 500, message: 'Failed to search handover orders', data: null },
      { status: 500 }
    );
  }
}

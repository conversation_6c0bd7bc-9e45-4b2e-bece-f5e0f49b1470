"use client";
import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";

// 定义订单数据类型
interface HandoverOrder {
  orderNo: string;
  orderType: 'pickup' | 'return';
  status: string;
  customer: string;
  type: string;
  info: string;
  details?: any;
}

export default function HandoverPage() {
  const searchParams = useSearchParams();
  const [input, setInput] = useState("");
  const [order, setOrder] = useState<HandoverOrder | null>(null);
  const [recipient, setRecipient] = useState<string>("");
  const [submitted, setSubmitted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showNoDataModal, setShowNoDataModal] = useState(false);

  // Autofill from query params
  useEffect(() => {
    const barcode = searchParams.get("barcode");
    if (barcode) {
      setInput(barcode);
      handleSearchWithCode(barcode);
    }
  }, [searchParams]);

  const handleSearchWithCode = async (code: string) => {
    if (!code.trim()) return;

    setLoading(true);
    try {
      const response = await fetch('/api/operations/handover/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pudoInternalCode: code.trim()
        }),
      });

      const result = await response.json();

      if (result.code === 200 && result.data) {
        setOrder(result.data);
        setShowNoDataModal(false);
      } else {
        setOrder(null);
        setShowNoDataModal(true);
      }
    } catch (error) {
      console.error('搜索订单失败:', error);
      setOrder(null);
      setShowNoDataModal(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    handleSearchWithCode(input);
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    setSubmitted(true);
  };

  return (
    <div className="max-w-xl mx-auto mt-0 bg-white rounded-xl shadow-lg p-8">
      <h1 className="text-2xl font-bold mb-6">Handover Parcel</h1>
      <div className="mb-6">
        <label className="block mb-2 font-medium">Scan or Enter Parcel/Order Number</label>
        <div className="flex gap-2">
          <input
            className="border rounded px-3 py-2 w-full"
            placeholder="Enter or scan order number"
            value={input}
            onChange={e => setInput(e.target.value)}
            onKeyDown={e => e.key === "Enter" && handleSearch()}
            disabled={!!order}
          />
          <button
            className="bg-blue-600 text-white px-4 py-2 rounded font-semibold hover:bg-blue-700"
            onClick={handleSearch}
            disabled={!!order}
          >
            Search
          </button>
        </div>
      </div>
      {order && (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="bg-green-50 border border-green-200 rounded p-3 mb-2 text-green-700">
            <div>Order found: <span className="font-semibold">{order.orderNo}</span></div>
            <div className="text-sm text-gray-700">Customer: {order.customer}</div>
            <div className="text-sm text-gray-700">Type: {order.type}</div>
            <div className="text-sm text-gray-700">Info: {order.info}</div>
          </div>
          <div className="bg-gray-100 border border-gray-300 rounded p-3 mb-2">
            <div className="font-semibold mb-2">Who do you want to hand over to?</div>
            <div className="flex gap-8 items-center justify-center mb-2">
              <label className="flex flex-col items-center cursor-pointer">
                <input
                  type="radio"
                  name="recipient"
                  value="courier"
                  checked={recipient === "courier"}
                  onChange={() => setRecipient("courier")}
                  className="mb-1"
                />
                <span className="text-3xl">🚚</span>
                <span className="text-sm mt-1">Courier</span>
              </label>
              <label className="flex flex-col items-center cursor-pointer">
                <input
                  type="radio"
                  name="recipient"
                  value="consumer"
                  checked={recipient === "consumer"}
                  onChange={() => setRecipient("consumer")}
                  className="mb-1"
                />
                <span className="text-3xl">👤</span>
                <span className="text-sm mt-1">Consumer</span>
              </label>
            </div>
            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-2 rounded font-semibold hover:bg-blue-700 mt-2"
              disabled={!recipient}
            >
              Submit
            </button>
          </div>
        </form>
      )}
      {submitted && (
        <div className="mt-6 bg-green-100 border border-green-300 text-green-800 rounded p-4 text-center font-semibold">
          Handover submitted successfully!
        </div>
      )}
      {/* DEV ONLY: Mock Data & Test Cases Card (bottom) */}
      <div className="mt-10 p-4 rounded-xl border-2 border-dashed bg-gradient-to-r from-yellow-100 to-pink-100 text-gray-800">
        <div className="font-bold mb-2">Mock Data & Test Cases (for UI layout only)</div>
        <ul className="text-sm list-disc pl-5 space-y-1">
          <li><span className="font-mono font-semibold">APITN00002</span>: Handover expired order to courier</li>
          <li><span className="font-mono font-semibold">APITN00003</span>: Handover expired order to consumer</li>
          <li><span className="font-mono font-semibold">APITN00004</span>: Handover return order</li>
        </ul>
        <div className="mt-2 text-xs text-gray-500">This card will be removed when real data is implemented.</div>
      </div>
    </div>
  );
} 